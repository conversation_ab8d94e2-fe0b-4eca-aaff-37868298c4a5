<script setup lang="ts">
import type { Payment } from '~/types/payment/payment.type'

definePageMeta({
  title: 'Pending Payments',
  preview: {
    title: 'Pending Payments',
    description: 'Review and confirm pending booking payments',
    categories: ['dashboards', 'bookings'],
    src: '/img/screens/dashboards-personal-3.png',
    srcDark: '/img/screens/dashboards-personal-3-dark.png',
    order: 23,
  },
})

const { $api } = useNuxtApp()
const { selectedSpace } = useSelectedSpace()
const { decrementCount } = usePendingBookingPayments()

// Reactive data
const isLoading = ref(true)
const payments = ref<Payment[]>([])
const totalPayments = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const selectedPayments = ref<string[]>([])
const showPaymentModal = ref(false)
const selectedPayment = ref<Payment | null>(null)
const processingId = ref<string | null>(null)

// Breadcrumb items
const breadcrumbItems = [
  {
    label: 'Dashboard',
    hideLabel: false,
    icon: 'lucide:home',
    to: '/app/dashboard',
  },
  {
    label: 'Spaces',
    hideLabel: false,
    to: '/app/spaces',
  },
  {
    label: 'Bookings',
    hideLabel: false,
    to: '/app/spaces/bookings',
  },
  {
    label: 'Pending Payments',
    hideLabel: false,
    to: '/app/spaces/bookings/pending-payments',
  },
]

// Filters
const filters = ref({
  search: '',
  paymentMethod: '',
  dateRange: undefined,
  minAmount: undefined,
  maxAmount: undefined,
})

// Quick filters
const quickFilters = ref<string[]>([])

const quickFilterOptions = [
  { id: 'urgent', label: 'Urgent (≤2 days)', icon: 'lucide:alert-triangle' },
  { id: 'this-week', label: 'This Week', icon: 'lucide:calendar-days' },
  { id: 'high-value', label: 'High Value (≥500 MAD)', icon: 'lucide:trending-up' },
  { id: 'cash-only', label: 'Cash Payments', icon: 'lucide:banknote' },
  { id: 'transfer-only', label: 'Bank Transfers', icon: 'lucide:building-2' },
]

// Table headers
const tableHeaders = [
  { label: 'Booking', key: 'booking', sortable: true, width: 'w-[300px]' },
  { label: 'Customer', key: 'customer', sortable: true, width: 'w-[200px]' },
  { label: 'Amount', key: 'amount', sortable: true, width: 'w-[120px]' },
  { label: 'Payment Method', key: 'paymentMethod', sortable: false, width: 'w-[150px]' },
  { label: 'Booking Date', key: 'bookingDate', sortable: true, width: 'w-[150px]' },
  { label: 'Created', key: 'created', sortable: true, width: 'w-[120px]' },
  { label: 'Priority', key: 'priority', sortable: false, width: 'w-[100px]' },
  { label: 'Actions', key: 'actions', sortable: false, width: 'w-[120px]' },
]

// Computed
const filteredPayments = computed(() => {
  let filtered = payments.value

  if (filters.value.search) {
    const search = filters.value.search.toLowerCase()
    filtered = filtered.filter(payment =>
      payment.booking?.user?.firstName?.toLowerCase().includes(search) ||
      payment.booking?.user?.lastName?.toLowerCase().includes(search) ||
      payment.booking?.user?.email?.toLowerCase().includes(search) ||
      payment.booking?.room?.name?.toLowerCase().includes(search) ||
      payment.booking?.bookingReference?.toLowerCase().includes(search)
    )
  }

  if (filters.value.paymentMethod) {
    filtered = filtered.filter(payment =>
      payment.paymentMethod === filters.value.paymentMethod
    )
  }

  if (filters.value.minAmount) {
    filtered = filtered.filter(payment =>
      parseFloat(payment.amount) >= filters.value.minAmount!
    )
  }

  if (filters.value.maxAmount) {
    filtered = filtered.filter(payment =>
      parseFloat(payment.amount) <= filters.value.maxAmount!
    )
  }

  // Apply quick filters
  if (quickFilters.value.length > 0) {
    const now = new Date()

    quickFilters.value.forEach(filterId => {
      switch (filterId) {
        case 'urgent':
          filtered = filtered.filter(payment => {
            const bookingDate = new Date(payment.booking?.startDateTime || '')
            const diffInDays = Math.ceil((bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
            return diffInDays <= 2
          })
          break

        case 'this-week':
          filtered = filtered.filter(payment => {
            const bookingDate = new Date(payment.booking?.startDateTime || '')
            const startOfWeek = new Date(now)
            startOfWeek.setDate(now.getDate() - now.getDay())
            startOfWeek.setHours(0, 0, 0, 0)
            const endOfWeek = new Date(startOfWeek)
            endOfWeek.setDate(startOfWeek.getDate() + 6)
            endOfWeek.setHours(23, 59, 59, 999)
            return bookingDate >= startOfWeek && bookingDate <= endOfWeek
          })
          break

        case 'high-value':
          filtered = filtered.filter(payment => parseFloat(payment.amount) >= 500)
          break

        case 'cash-only':
          filtered = filtered.filter(payment => payment.paymentMethod === 'cash')
          break

        case 'transfer-only':
          filtered = filtered.filter(payment => payment.paymentMethod === 'transfer')
          break
      }
    })
  }

  return filtered
})

const allSelected = computed(() =>
  selectedPayments.value.length > 0 &&
  selectedPayments.value.length === filteredPayments.value.length
)

// Methods
const fetchPayments = async () => {
  if (!selectedSpace.value?.id) return

  try {
    isLoading.value = true

    // Use the new dedicated endpoint for pending booking payments
    const response = await $api.payment.getPendingBookingPaymentsBySpace(selectedSpace.value.id)

    if (response?.success && response.data) {
      payments.value = response.data
      totalPayments.value = response.count || response.data.length
    } else {
      payments.value = []
      totalPayments.value = 0
    }
  } catch (error) {
    console.error('Error fetching pending payments:', error)
    payments.value = []
    totalPayments.value = 0
  } finally {
    isLoading.value = false
  }
}

const confirmPayment = async (payment: Payment) => {
  try {
    processingId.value = payment.id

    // Confirm the payment using the dedicated confirmation endpoint
    await $api.payment.confirmBookingPayment(payment.id)

    // Remove from pending list
    payments.value = payments.value.filter(p => p.id !== payment.id)
    totalPayments.value--

    // Update the sidebar count
    decrementCount()

    // Close modal if open
    if (showPaymentModal.value) {
      showPaymentModal.value = false
      selectedPayment.value = null
    }

    console.log('Payment confirmed successfully')
  } catch (error) {
    console.error('Failed to confirm payment:', error)
    // TODO: Add toast notification for error
  } finally {
    processingId.value = null
  }
}

const viewPaymentDetails = (payment: Payment) => {
  selectedPayment.value = payment
  showPaymentModal.value = true
}

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedPayments.value = []
  } else {
    selectedPayments.value = filteredPayments.value.map(p => p.id)
  }
}

const formatCurrency = (amount: string | number) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount
  return new Intl.NumberFormat('fr-MA', {
    style: 'currency',
    currency: 'MAD',
  }).format(numAmount)
}

const formatDate = (date: Date | string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Helper function to format datetime like dashboard
const formatDateTime = (datetime: string) => {
  if (!datetime) return '-'
  return new Date(datetime).toLocaleString('en-GB', {
    hour: 'numeric',
    minute: 'numeric',
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  })
}

// Helper function to format date like dashboard
const formatDateOnly = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  })
}

const getBookingPriority = (startDateTime: string) => {
  const now = new Date()
  const bookingDate = new Date(startDateTime)
  const diffInDays = Math.ceil((bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffInDays <= 1) return 'urgent'
  if (diffInDays <= 3) return 'high'
  if (diffInDays <= 7) return 'medium'
  return 'low'
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'danger'
    case 'high': return 'warning'
    case 'medium': return 'info'
    default: return 'muted'
  }
}

const getPriorityStripeClass = (priority: string) => {
  switch (priority) {
    case 'urgent': return 'border-l-4 border-l-red-500'
    case 'high': return 'border-l-4 border-l-orange-500'
    case 'medium': return 'border-l-4 border-l-yellow-500'
    default: return ''
  }
}

const getPaymentMethodColor = (method: string) => {
  switch (method?.toLowerCase()) {
    case 'cash': return 'success'
    case 'transfer': return 'info'
    case 'card': return 'primary'
    default: return 'muted'
  }
}

const getPaymentMethodIcon = (method: string) => {
  switch (method?.toLowerCase()) {
    case 'cash': return 'lucide:banknote'
    case 'transfer': return 'lucide:building-2'
    case 'card': return 'lucide:credit-card'
    default: return 'lucide:help-circle'
  }
}

const clearFilters = () => {
  filters.value = {
    search: '',
    paymentMethod: '',
    dateRange: undefined,
    minAmount: undefined,
    maxAmount: undefined,
  }
  quickFilters.value = []
}

// Lifecycle
onMounted(() => {
  fetchPayments()
})

watch(() => selectedSpace.value?.id, (newSpaceId) => {
  if (newSpaceId) {
    fetchPayments()
  }
})

watch(currentPage, () => {
  fetchPayments()
})
</script>

<template>
  <div class="w-full pb-24">
    <!-- Header Section -->
    <div class="mb-6 flex flex-col gap-6 sm:flex-row sm:items-center sm:justify-between">
      <div>
        <BaseBreadcrumb :items="breadcrumbItems" class="mb-4">
          <Icon name="lucide:chevron-right" class="block size-3" />
        </BaseBreadcrumb>

        <BaseHeading
          as="h1"
          size="2xl"
          weight="semibold"
          class="text-muted-900 mb-2 dark:text-white"
        >
          Pending Payments
        </BaseHeading>
        <BaseParagraph size="sm" class="text-muted-500 hidden sm:block">
          Review and confirm pending booking payments for your spaces
        </BaseParagraph>
      </div>

      <div class="flex items-center gap-4">
        <BaseBadge color="warning" size="lg" class="px-3 py-1.5">
          {{ totalPayments }} Pending
        </BaseBadge>

        <!-- Bulk Actions - Only show when items are selected -->
        <div v-if="selectedPayments.length > 0" class="flex items-center gap-3">
          <div class="bg-muted-200 dark:bg-muted-700 h-6 w-px" />
          <BaseText size="sm" class="text-muted-500 font-medium">
            {{ selectedPayments.length }} selected
          </BaseText>
          <BaseButton
            color="success"
            variant="pastel"
            size="sm"
            class="px-4 py-2"
          >
            <Icon name="lucide:check" class="h-4 w-4 mr-2" />
            Confirm Selected
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <TairoContentWrapper>
      <div class="mb-8 space-y-6">
        <!-- Search and Quick Filters -->
        <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div class="flex-1 max-w-md">
            <BaseInput
              v-model="filters.search"
              icon="lucide:search"
              placeholder="Search bookings, customers, or references..."
              rounded="lg"
            />
          </div>

          <!-- Quick Filters -->
          <div class="flex flex-wrap gap-2">
            <BaseButton
              v-for="filter in quickFilterOptions"
              :key="filter.id"
              :color="quickFilters.includes(filter.id) ? 'primary' : 'muted'"
              :variant="quickFilters.includes(filter.id) ? 'solid' : 'pastel'"
              size="sm"
              @click="() => {
                const index = quickFilters.indexOf(filter.id)
                if (index > -1) {
                  quickFilters.splice(index, 1)
                } else {
                  quickFilters.push(filter.id)
                }
              }"
            >
              <Icon :name="filter.icon" class="h-4 w-4 mr-2" />
              {{ filter.label }}
            </BaseButton>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <BaseInput
            v-model="filters.minAmount"
            type="number"
            placeholder="Min amount (MAD)"
            rounded="lg"
          />
          <BaseInput
            v-model="filters.maxAmount"
            type="number"
            placeholder="Max amount (MAD)"
            rounded="lg"
          />
          <BaseButton
            v-if="Object.values(filters).some(v => v) || quickFilters.length > 0"
            color="muted"
            variant="pastel"
            @click="clearFilters"
          >
            <Icon name="lucide:x" class="h-4 w-4 mr-2" />
            Clear Filters
          </BaseButton>
        </div>
      </div>

      <div>
        <!-- Loading State -->
        <div v-if="isLoading" class="flex justify-center py-12">
          <BaseLoader size="lg" />
        </div>

        <!-- Empty State -->
        <div v-else-if="filteredPayments.length === 0">
          <BasePlaceholderPage
            title="No pending payments"
            subtitle="All booking payments have been processed or no payments match your search criteria."
          >
            <template #image>
              <img
                class="block dark:hidden"
                src="/img/illustrations/placeholders/flat/placeholder-search-4.svg"
                alt="No payments"
              >
              <img
                class="hidden dark:block"
                src="/img/illustrations/placeholders/flat/placeholder-search-4-dark.svg"
                alt="No payments"
              >
            </template>
          </BasePlaceholderPage>
        </div>

        <!-- Table -->
        <div v-else class="bg-white dark:bg-muted-800 border border-muted-200 dark:border-muted-700 rounded-xl overflow-hidden">
          <!-- Table Header with Actions -->
          <div class="border-muted-200 dark:border-muted-700 border-b px-6 py-4">
            <div class="flex w-full items-center justify-between">
              <!-- Legend -->
              <div class="flex items-center gap-6">
                <div class="flex items-center gap-4">
                  <div class="flex items-center gap-1">
                    <div class="w-2 h-2 bg-red-500 rounded-sm"></div>
                    <BaseText size="xs" class="text-muted-500">Urgent</BaseText>
                  </div>
                  <div class="flex items-center gap-1">
                    <div class="w-2 h-2 bg-orange-500 rounded-sm"></div>
                    <BaseText size="xs" class="text-muted-500">≤3d</BaseText>
                  </div>
                  <div class="flex items-center gap-1">
                    <div class="w-2 h-2 bg-yellow-500 rounded-sm"></div>
                    <BaseText size="xs" class="text-muted-500">≤7d</BaseText>
                  </div>
                </div>
              </div>

              <!-- Desktop Actions -->
              <div class="hidden w-full items-center justify-end gap-4 sm:flex sm:w-auto">
                <BaseButtonGroup class="shadow-sm">
                  <BaseButtonAction
                    rounded="md"
                    :disabled="isLoading"
                    @click="fetchPayments"
                  >
                    <Icon
                      :name="isLoading ? 'ph:spinner-gap-duotone' : 'ph:arrows-clockwise-duotone'"
                      class="size-5"
                      :class="{ 'animate-spin': isLoading }"
                    />
                  </BaseButtonAction>
                </BaseButtonGroup>
              </div>
            </div>
          </div>

          <!-- Table -->
          <table class="w-full text-left">
            <thead>
              <tr class="bg-muted-50 dark:bg-muted-800 border-muted-200 dark:border-muted-700 border-b">
                <!-- Selection column -->
                <th class="w-[60px] p-4">
                  <BaseCheckbox
                    :model-value="allSelected"
                    :indeterminate="selectedPayments.length > 0 && selectedPayments.length < filteredPayments.length"
                    @update:model-value="toggleSelectAll"
                  />
                </th>
                <th
                  v-for="header in tableHeaders"
                  :key="header.label"
                  class="group p-4 text-left"
                  :class="[header.width]"
                >
                  <div class="flex items-center gap-2">
                    <span class="text-muted-400 dark:text-muted-300 font-sans text-xs font-semibold uppercase tracking-wider">
                      {{ header.label }}
                    </span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-muted-200 dark:divide-muted-700">
              <tr
                v-for="payment in filteredPayments"
                :key="payment.id"
                class="hover:bg-muted-50 dark:hover:bg-muted-800/50 transition-colors duration-300"
                :class="[
                  { 'bg-primary-50 dark:bg-primary-500/10': selectedPayments.includes(payment.id) },
                  getPriorityStripeClass(getBookingPriority(payment.booking?.startDateTime || ''))
                ]"
              >
                <!-- Selection cell -->
                <td class="p-4">
                  <BaseCheckbox
                    :model-value="selectedPayments.includes(payment.id)"
                    @update:model-value="(checked) => {
                      if (checked) {
                        selectedPayments.push(payment.id)
                      } else {
                        selectedPayments = selectedPayments.filter(id => id !== payment.id)
                      }
                    }"
                  />
                </td>

                <!-- Booking cell -->
                <td class="p-4">
                  <BaseText size="sm" weight="medium" class="text-muted-900 dark:text-white font-mono">
                    {{ payment.booking?.bookingReference || 'N/A' }}
                  </BaseText>
                </td>

                <!-- Customer cell -->
                <td class="p-4">
                  <div class="flex items-center gap-3">
                    <BaseAvatar
                      :text="`${payment.booking?.user?.firstName?.charAt(0) || ''}${payment.booking?.user?.lastName?.charAt(0) || ''}`"
                      size="sm"
                    />
                    <div class="min-w-0 flex-1">
                      <BaseText size="sm" weight="medium" class="text-muted-900 dark:text-white block">
                        {{ `${payment.booking?.user?.firstName || ''} ${payment.booking?.user?.lastName || ''}`.trim() || 'Unknown User' }}
                      </BaseText>
                      <BaseText size="xs" class="text-muted-500 block">
                        {{ payment.booking?.user?.email || 'No email' }}
                      </BaseText>
                    </div>
                  </div>
                </td>

                <!-- Amount cell -->
                <td class="p-4">
                  <BaseText size="sm" weight="semibold" class="text-muted-900 dark:text-white">
                    {{ formatCurrency(payment.amount) }}
                  </BaseText>
                </td>

                <!-- Payment Method cell -->
                <td class="p-4">
                  <BaseTag
                    :color="getPaymentMethodColor(payment.paymentMethod)"
                    rounded="full"
                    size="sm"
                    variant="pastel"
                  >
                    <template #start>
                      <Icon
                        :name="getPaymentMethodIcon(payment.paymentMethod)"
                        class="h-3 w-3"
                      />
                    </template>
                    {{ payment.paymentMethod?.charAt(0).toUpperCase() + payment.paymentMethod?.slice(1) || 'Unknown' }}
                  </BaseTag>
                </td>

                <!-- Booking Date cell -->
                <td class="p-4">
                  <BaseText
                    size="sm"
                    weight="medium"
                    lead="none"
                    class="text-muted-500"
                  >
                    {{ formatDateTime(payment.booking?.startDateTime || '') }}
                  </BaseText>
                </td>

                <!-- Created cell -->
                <td class="p-4">
                  <BaseText
                    size="sm"
                    weight="medium"
                    lead="none"
                    class="text-muted-500"
                  >
                    {{ formatDateOnly(payment.createdAt) }}
                  </BaseText>
                </td>

                <!-- Priority cell -->
                <td class="p-4">
                  <BaseBadge
                    :color="getPriorityColor(getBookingPriority(payment.booking?.startDateTime || ''))"
                    size="sm"
                  >
                    {{ getBookingPriority(payment.booking?.startDateTime || '') }}
                  </BaseBadge>
                </td>

                <!-- Actions cell -->
                <td class="p-4">
                  <div class="flex w-full justify-end">
                    <BaseDropdown
                      variant="context"
                      label="Actions"
                      placement="bottom-end"
                    >
                      <BaseDropdownItem
                        title="View Details"
                        text="View payment details"
                        rounded="sm"
                        @click="viewPaymentDetails(payment)"
                      >
                        <template #start>
                          <Icon name="ph:eye-duotone" class="me-2 block size-5" />
                        </template>
                      </BaseDropdownItem>

                      <!-- Show only the relevant confirmation button based on payment method -->
                      <BaseDropdownItem
                        v-if="payment.paymentMethod === 'cash'"
                        title="Confirm Cash Payment"
                        text="Mark cash payment as received"
                        rounded="sm"
                        color="success"
                        @click="confirmPayment(payment)"
                        :disabled="processingId === payment.id"
                      >
                        <template #start>
                          <Icon name="ph:money-wavy-duotone" class="me-2 block size-5" />
                        </template>
                      </BaseDropdownItem>

                      <BaseDropdownItem
                        v-if="payment.paymentMethod === 'transfer'"
                        title="Confirm Transfer"
                        text="Mark bank transfer as received"
                        rounded="sm"
                        color="success"
                        @click="confirmPayment(payment)"
                        :disabled="processingId === payment.id"
                      >
                        <template #start>
                          <Icon name="ph:bank-duotone" class="me-2 block size-5" />
                        </template>
                      </BaseDropdownItem>
                    </BaseDropdown>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div v-if="!isLoading && filteredPayments.length > 0 && totalPayments > pageSize" class="mt-8">
          <BasePagination
            :total-items="totalPayments"
            :item-per-page="pageSize"
            :current-page="currentPage"
            rounded="lg"
            @update:current-page="currentPage = $event"
          />
        </div>
      </div>
    </TairoContentWrapper>

    <!-- Enhanced Payment Details Modal -->
    <TairoModal :open="showPaymentModal" size="lg" @close="showPaymentModal = false">
      <template #header>
        <div class="flex w-full items-center justify-between p-6 border-b border-muted-200 dark:border-muted-700">
          <div class="flex items-center gap-3">
            <div class="size-10 rounded-xl bg-primary-100 dark:bg-primary-500/20 flex items-center justify-center">
              <Icon name="ph:credit-card-duotone" class="size-5 text-primary-600 dark:text-primary-400" />
            </div>
            <div>
              <h3 class="font-heading text-muted-900 text-xl font-semibold leading-6 dark:text-white">
                Payment Confirmation
              </h3>
              <BaseText size="sm" class="text-muted-500">
                Review and confirm payment details
              </BaseText>
            </div>
          </div>
          <BaseButtonClose @click="showPaymentModal = false" />
        </div>
      </template>
      <div class="p-6" v-if="selectedPayment">
        <div class="space-y-6">
          <!-- Compact Status Banner -->
          <div class="flex items-center justify-between p-3 rounded-lg bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800/50">
            <div class="flex items-center gap-2">
              <Icon name="ph:clock-duotone" class="size-4 text-warning-600 dark:text-warning-400" />
              <BaseText size="sm" weight="medium" class="text-warning-700 dark:text-warning-300">
                Pending confirmation
              </BaseText>
            </div>
            <BaseTag
              :color="getPaymentMethodColor(selectedPayment.paymentMethod)"
              rounded="full"
              size="sm"
              variant="pastel"
            >
              <template #start>
                <Icon
                  :name="getPaymentMethodIcon(selectedPayment.paymentMethod)"
                  class="size-3"
                />
              </template>
              {{ selectedPayment.paymentMethod?.charAt(0).toUpperCase() + selectedPayment.paymentMethod?.slice(1) || 'Unknown' }}
            </BaseTag>
          </div>

          <!-- Big Payment Amount Card -->
          <div class="rounded-2xl bg-gradient-to-br from-muted-50 to-muted-100/50 dark:from-muted-800 dark:to-muted-900/50 border border-muted-200 dark:border-muted-700 p-6">
            <div class="text-center">
              <BaseText size="sm" class="text-muted-500 mb-2">Payment Amount</BaseText>
              <div class="text-4xl font-bold text-muted-900 dark:text-white mb-2">
                {{ formatCurrency(selectedPayment.amount) }}
              </div>
              <BaseText size="sm" class="text-muted-600 dark:text-muted-400">
                MAD (Moroccan Dirham)
              </BaseText>
            </div>
          </div>

          <!-- Compact Details Section -->
          <div class="space-y-4">
            <!-- Customer & Booking Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Customer -->
              <div class="p-4 rounded-xl bg-white dark:bg-muted-800 border border-muted-200 dark:border-muted-700">
                <div class="flex items-center gap-3">
                  <BaseAvatar
                    :text="`${selectedPayment.booking?.user?.firstName?.charAt(0) || ''}${selectedPayment.booking?.user?.lastName?.charAt(0) || ''}`"
                    size="sm"
                    class="bg-primary-100 text-primary-600 dark:bg-primary-500/20"
                  />
                  <div class="flex-1 min-w-0">
                    <BaseText weight="semibold" class="text-muted-900 dark:text-white block">
                      {{ `${selectedPayment.booking?.user?.firstName || ''} ${selectedPayment.booking?.user?.lastName || ''}`.trim() || 'Unknown User' }}
                    </BaseText>
                    <BaseText size="sm" class="text-muted-500 block truncate">
                      {{ selectedPayment.booking?.user?.email || 'No email' }}
                    </BaseText>
                  </div>
                </div>
              </div>

              <!-- Booking Details -->
              <div class="p-4 rounded-xl bg-white dark:bg-muted-800 border border-muted-200 dark:border-muted-700">
                <div class="space-y-2">
                  <div>
                    <BaseText weight="semibold" class="text-muted-900 dark:text-white block">
                      {{ selectedPayment.booking?.room?.name || 'Unknown Room' }}
                    </BaseText>
                    <BaseText size="sm" class="text-muted-500 block">
                      Ref: {{ selectedPayment.booking?.bookingReference }}
                    </BaseText>
                  </div>
                  <div>
                    <BaseText size="sm" class="text-muted-600 dark:text-muted-400 block">
                      {{ formatDateTime(selectedPayment.booking?.startDateTime || '') }}
                    </BaseText>
                    <BaseText size="sm" class="text-muted-500 block">
                      Duration: {{ selectedPayment.booking?.quantity || 1 }} hour(s)
                    </BaseText>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Details -->
            <div class="p-4 rounded-xl bg-muted-50 dark:bg-muted-800/50 border border-muted-200 dark:border-muted-700">
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <BaseText size="xs" class="text-muted-500 uppercase tracking-wider block mb-1">Status</BaseText>
                  <BaseBadge color="warning" size="sm">
                    {{ selectedPayment.status?.charAt(0).toUpperCase() + selectedPayment.status?.slice(1) || 'Pending' }}
                  </BaseBadge>
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-500 uppercase tracking-wider block mb-1">Method</BaseText>
                  <BaseText weight="medium" class="text-muted-900 dark:text-white block">
                    {{ selectedPayment.paymentMethod?.charAt(0).toUpperCase() + selectedPayment.paymentMethod?.slice(1) || 'Unknown' }}
                  </BaseText>
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-500 uppercase tracking-wider block mb-1">Created</BaseText>
                  <BaseText weight="medium" class="text-muted-900 dark:text-white block">
                    {{ formatDateOnly(selectedPayment.createdAt) }}
                  </BaseText>
                </div>
                <div>
                  <BaseText size="xs" class="text-muted-500 uppercase tracking-wider block mb-1">Receipt</BaseText>
                  <BaseText weight="medium" class="text-muted-900 dark:text-white font-mono text-xs block">
                    {{ selectedPayment.receiptNumber || 'N/A' }}
                  </BaseText>
                </div>
              </div>
            </div>
          </div>

          <!-- Enhanced Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-muted-200 dark:border-muted-700">
            <BaseButton
              @click="showPaymentModal = false"
              color="muted"
              variant="pastel"
              size="lg"
              class="flex-1 order-2 sm:order-1"
            >
              <Icon name="ph:x-duotone" class="size-4 mr-2" />
              Cancel
            </BaseButton>
            <BaseButton
              @click="confirmPayment(selectedPayment)"
              :disabled="processingId === selectedPayment?.id"
              color="success"
              size="lg"
              class="flex-1 order-1 sm:order-2 shadow-lg"
            >
              <Icon
                v-if="processingId === selectedPayment?.id"
                name="ph:spinner-gap-duotone"
                class="size-4 mr-2 animate-spin"
              />
              <Icon
                v-else-if="selectedPayment?.paymentMethod === 'cash'"
                name="ph:money-wavy-duotone"
                class="size-4 mr-2"
              />
              <Icon
                v-else
                name="ph:bank-duotone"
                class="size-4 mr-2"
              />
              {{ processingId === selectedPayment?.id ? 'Processing...' :
                 selectedPayment?.paymentMethod === 'cash' ? 'Confirm Cash Payment' : 'Confirm Transfer' }}
            </BaseButton>
          </div>
        </div>
      </div>
    </TairoModal>
  </div>
</template>
